.ext-wikisource-bulkocr-container {
	margin-bottom: 1em;
	margin-top: 1em;
	border: 1px dashed #808080;
	padding: 8px;
}

.ext-wikisource-BulkOcrWidget {
	display: inline-block;
	margin-bottom: 0.5em;
}

.ext-wikisource-bulkocr-config-panel {
	min-width: 250px;
	padding: 10px;
	overflow: visible;
}

.ext-wikisource-bulkocr-engine-label {
	margin-bottom: 0.5em;
	display: block;
}

.ext-wikisource-bulkocr-engineradios {
	margin-bottom: 1em;
	font-weight: bolder;
}

.ext-wikisource-bulkocr-more-options {
	margin-top: 0.5em;
}

.ext-wikisource-bulkocr-more-options .oo-ui-fieldLayout {
	margin-bottom: 0.5em;
}

/* Ensure dropdown menus appear above other elements */
.ext-wikisource-bulkocr-more-options .oo-ui-menuSelectWidget,
.ext-wikisource-bulkocr-more-options .oo-ui-textInputMenuSelectWidget {
	z-index: 3;
}

/* Fix for dropdown menu visibility */
.oo-ui-popupWidget-popup,
.oo-ui-popupWidget-anchor {
	overflow: visible;
}
