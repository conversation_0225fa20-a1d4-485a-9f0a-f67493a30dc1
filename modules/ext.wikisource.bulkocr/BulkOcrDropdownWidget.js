var Langs = require( '../ext.wikisource.OCR/Langs.json' );

/**
 * Dropdown configuration widget for Bulk OCR functionality
 *
 * @class
 * @constructor
 * @param {Object} config Configuration options
 */
class BulkOcrDropdownWidget {
	constructor( config ) {
		config = config || {};

		// Initialize default settings
		this.selectedEngine = config.defaultEngine || 'google';
		this.selectedLanguages = config.defaultLanguages || [];
		this.useLineDetection = config.useLineDetection || false;

		// Available languages/models for engines (same as OcrTool)
		this.allLangs = Langs;

		// Create the dropdown configuration button
		this.configButton = new OO.ui.PopupButtonWidget( {
			indicator: 'down',
			title: mw.msg( 'wikisource-ocr-settings-menu' ),
			invisibleLabel: true,
			$overlay: OO.ui.getDefaultOverlay(),
			popup: {
				anchor: false,
				$content: this.getConfigContent().$element,
				padded: false,
				align: 'force-left'
			}
		} );

		// Replace the built-in click handler with our own
		this.configButton.disconnect( this.configButton, { click: 'onAction' } );
		this.configButton.connect( this, { click: 'onClickConfigButton' } );

		// Handle popup closing to save config and disable radio select
		this.configButton.popup.connect( this, { closing: () => {
			this.radioSelect.setDisabled( true );
		} } );
	}

	/**
	 * Get the configuration content for the dropdown popup
	 *
	 * @return {OO.ui.PanelLayout} The configuration panel
	 */
	getConfigContent() {
		// Create OCR engine options
		let ocrOptions = [
			new OO.ui.RadioOptionWidget( {
				data: 'tesseract',
				label: mw.msg( 'wikisource-ocr-engine-tesseract' )
			} ),
			new OO.ui.RadioOptionWidget( {
				data: 'google',
				label: mw.msg( 'wikisource-ocr-engine-google' )
			} ),
			new OO.ui.RadioOptionWidget( {
				data: 'transkribus',
				label: mw.msg( 'wikisource-ocr-engine-transkribus' )
			} )
		];

		// Create radio select widget for engines
		this.radioSelect = new OO.ui.RadioSelectWidget( {
			classes: [ 'ext-wikisource-bulkocr-engineradios' ],
			items: ocrOptions
		} );

		// Create engine label
		var engineLabel = new OO.ui.LabelWidget( {
			classes: [ 'ext-wikisource-bulkocr-engine-label' ],
			label: mw.msg( 'wikisource-ocr-engine' ),
			input: this.radioSelect
		} );

		// Connect engine selection handler
		this.radioSelect.connect( this, {
			choose: 'onEngineChoose'
		} );
		this.radioSelect.selectItemByData( this.selectedEngine );

		// Create more options fieldset for languages
		this.moreOptionsFieldset = new OO.ui.FieldsetLayout( {
			classes: [ 'ext-wikisource-bulkocr-more-options' ]
		} );

		// Create fieldset for line detection checkbox (Transkribus only)
		this.lineDetectionFieldset = new OO.ui.FieldsetLayout( {} );
		this.lineDetectionCheckBox = new OO.ui.CheckboxInputWidget( {
			selected: this.useLineDetection
		} );
		this.lineDetectionFieldset.addItems( [
			new OO.ui.FieldLayout( this.lineDetectionCheckBox, {
				label: mw.msg( 'wikisource-ocr-engine-line-model-checkbox-label' ),
				align: 'inline'
			} )
		] );
		this.lineDetectionCheckBox.connect( this, {
			change: 'setLineDetectionModel'
		} );

		// Show/hide line detection based on selected engine
		this.lineDetectionFieldset.toggle( this.selectedEngine === 'transkribus' );

		// Update language options based on selected engine
		this.updateLanguageOptions();

		// Create the content panel
		var content = new OO.ui.PanelLayout( {
			padded: false,
			expanded: false,
			classes: [ 'ext-wikisource-bulkocr-config-panel' ]
		} );

		content.$element.append(
			engineLabel.$element,
			this.radioSelect.$element,
			this.lineDetectionFieldset.$element,
			this.moreOptionsFieldset.$element
		);

		return content;
	}

	/**
	 * Update language options based on selected engine
	 */
	updateLanguageOptions() {
		this.moreOptionsFieldset.clearItems();
		let options = this.getLanguages( this.selectedEngine );
		let fieldLabel = this.setLanguageDropdownLabel( this.selectedEngine );
		let selectedLanguages = this.sortSelectedLangs( this.selectedEngine );

		if ( this.selectedEngine !== 'transkribus' ) {
			// Multi-select for non-Transkribus engines
			this.languageDropdown = new OO.ui.MenuTagMultiselectWidget( {
				options: options
			} );
			this.languageDropdown.connect( this, {
				change: 'setLanguages'
			} );
			this.languageDropdown.setValue( selectedLanguages );
		} else {
			// Single select for Transkribus
			this.languageDropdown = new OO.ui.DropdownInputWidget( {
				options: options
			} );
			this.languageDropdown.connect( this, {
				change: 'setModel'
			} );
			if ( selectedLanguages[ 0 ] !== undefined ) {
				let value = selectedLanguages[ 0 ].data;
				this.languageDropdown.setValue( value );
			}
		}

		this.moreOptionsFieldset.addItems( [
			new OO.ui.FieldLayout( this.languageDropdown, {
				label: fieldLabel,
				align: 'inline'
			} )
		] );
	}

	/**
	 * Set the language dropdown label based on engine
	 *
	 * @param {string} engine The selected engine
	 * @return {string} The field label
	 */
	setLanguageDropdownLabel( engine ) {
		let fieldLabel = mw.msg( 'wikisource-ocr-language-dropdown-label' );
		if ( engine === 'transkribus' ) {
			fieldLabel = mw.msg( 'wikisource-ocr-model-dropdown-label' );
		}
		return fieldLabel;
	}

	/**
	 * Get available languages for the selected engine
	 *
	 * @param {string} engine The selected engine
	 * @return {Array} Array of language options
	 */
	getLanguages( engine ) {
		let items = [];
		let engineLanguages = this.allLangs[ engine ];
		for ( let key in engineLanguages ) {
			let data = engineLanguages[ key ];
			items.push( {
				data: key,
				label: data
			} );
		}
		return items;
	}

	/**
	 * Sort selected languages for the current engine
	 *
	 * @param {string} engine The selected engine
	 * @return {Array} Array of selected language objects
	 */
	sortSelectedLangs( engine ) {
		let selectedLangs = this.selectedLanguages;
		let langs = [];
		let engineLanguages = this.allLangs[ engine ];
		for ( let langIndex in selectedLangs ) {
			for ( let key in engineLanguages ) {
				if ( key === selectedLangs[ langIndex ] ) {
					langs.push( {
						data: key,
						label: engineLanguages[ key ]
					} );
				}
			}
		}
		return langs;
	}

	/**
	 * Handle clicking the configuration dropdown button
	 */
	onClickConfigButton() {
		// Toggle the popup
		this.configButton.popup.toggle();

		// Enable the radio select widget based on popup visibility
		this.radioSelect.setDisabled( !this.configButton.popup.isVisible() );
		// Focus the radio button on clicking the config button
		this.radioSelect.focus();
	}

	/**
	 * Handle engine selection change
	 *
	 * @param {OO.ui.OptionWidget} item Chosen item
	 * @param {boolean} selected Item is selected
	 */
	onEngineChoose( item, selected ) {
		if ( selected ) {
			this.selectedEngine = item.data;
			// Show/hide line detection checkbox based on engine
			this.lineDetectionFieldset.toggle( this.selectedEngine === 'transkribus' );
			// Update language options
			this.updateLanguageOptions();
		}
	}

	/**
	 * Handle line detection model checkbox change
	 */
	setLineDetectionModel() {
		this.useLineDetection = this.lineDetectionCheckBox.isSelected();
	}

	/**
	 * Handle language selection change (multi-select)
	 *
	 * @param {Array} items Selected language items
	 */
	setLanguages( items ) {
		let langs = [];
		items.forEach( element => {
			langs.push( element.data );
		} );
		this.selectedLanguages = langs;
	}

	/**
	 * Handle model selection change (single select for Transkribus)
	 *
	 * @param {string} value Selected model value
	 */
	setModel( value ) {
		this.selectedLanguages = [ value ];
	}

	/**
	 * Get the currently selected engine
	 *
	 * @return {string} The selected engine
	 */
	getSelectedEngine() {
		return this.selectedEngine;
	}

	/**
	 * Get the currently selected languages
	 *
	 * @return {Array} The selected languages
	 */
	getSelectedLanguages() {
		return this.selectedLanguages;
	}

	/**
	 * Get the line detection setting
	 *
	 * @return {boolean} Whether line detection is enabled
	 */
	getUseLineDetection() {
		return this.useLineDetection;
	}

	/**
	 * Get the dropdown button element
	 *
	 * @return {OO.ui.PopupButtonWidget} The dropdown button
	 */
	getButton() {
		return this.configButton;
	}
}

module.exports = BulkOcrDropdownWidget;
