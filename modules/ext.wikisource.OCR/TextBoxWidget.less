@import 'mediawiki.skin.variables.less';

.ext-wikisource-TextBoxWidget {
	background-color: @background-color-interactive;
	display: flex;
	align-items: center;
	bottom: auto;
	// TODO: We're not using `bolder` in Codex Design System for Wikimedia.
	font-weight: bolder;
	border-color: @border-color-subtle;
	border-width: @border-width-base 0;
	border-style: @border-style-base;

	.ext-wikisource-TextBoxWidget-icon {
		margin: 0 1em;
	}

	.oo-ui-labelElement {
		margin-right: 2em;
	}

	&.ext-wikisource-UndoWidget {
		justify-content: space-between;
		padding-left: 8px;
	}
}
